name: Build Package

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11']

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.2
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Configure Poetry
        run: |
          poetry config virtualenvs.in-project true
          
      - name: Install dependencies
        run: |
          poetry install --no-interaction --with dev

      - name: Build package
        run: |
          poetry build
          ls -la dist/

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: uml-mcp-dist-${{ matrix.python-version }}
          path: dist/
          retention-days: 5