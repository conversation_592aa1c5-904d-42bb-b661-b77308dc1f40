name: UML-MCP CD Pipeline

on:
  release:
    types: [created]
  workflow_dispatch:
    inputs:
      version_increment:
        description: 'Version increment type (patch, minor, major)'
        required: true
        default: 'patch'
      deploy_to_smithery:
        description: 'Deploy to <PERSON><PERSON> after build'
        type: boolean
        required: false
        default: false
      publish_to_pypi:
        description: 'Publish to PyPI after build'
        type: boolean
        required: false
        default: false

jobs:
  publish:
    needs: build
    if: |
      (github.event_name == 'workflow_dispatch' && github.event.inputs.publish_to_pypi == 'true') ||
      github.event_name == 'release'
    uses: ./.github/workflows/publish.yml
    with:
      version_increment: ${{ github.event.inputs.version_increment }}
    secrets: inherit

  deploy:
    needs: build
    if: |
      (github.event_name == 'push' && github.ref == 'refs/heads/main') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy_to_smithery == 'true')
    uses: ./.github/workflows/deploy.yml
    secrets: inherit