{"name": "VAE Diagram", "version": "1.0", "nodes": [{"label": "Input", "type": "RoundRectBlock", "x": 50, "y": 125, "width": 100, "height": 50, "angle": 0, "textOrientation": "horizontal"}, {"label": "Encoder", "type": "EncoderBlock", "x": 200, "y": 125, "width": 100, "height": 50, "angle": -90, "indent": 20, "textOrientation": "vertical_up"}, {"label": "Latent Space", "type": "LatentCubeBlock", "x": 350, "y": 125, "width": 100, "height": 50, "angle": 0, "textOrientation": "horizontal"}, {"label": "Decoder", "type": "DecoderBlock", "x": 500, "y": 125, "width": 100, "height": 50, "angle": -90, "indent": 20, "textOrientation": "vertical_down"}, {"label": "Output", "type": "RoundRectBlock", "x": 650, "y": 125, "width": 100, "height": 50, "angle": 0, "textOrientation": "horizontal"}], "connections": [{"from": "Input", "to": "Encoder"}, {"from": "Encoder", "to": "Latent Space"}, {"from": "Latent Space", "to": "Decoder"}, {"from": "Decoder", "to": "Output"}], "sample_config": {"nodes": [{"label": "Input", "x": 50, "y": 125, "width": 100, "height": 50, "angle": 0, "type": "RoundRectBlock"}, {"label": "Encoder", "x": 200, "y": 125, "width": 100, "height": 50, "angle": 0, "type": "EncoderBlock", "indent": 20}, {"label": "Latent", "x": 350, "y": 125, "width": 100, "height": 50, "angle": 0, "type": "LatentCubeBlock"}, {"label": "Decoder", "x": 500, "y": 125, "width": 100, "height": 50, "angle": 0, "type": "DecoderBlock", "indent": 20}, {"label": "Output", "x": 650, "y": 125, "width": 100, "height": 50, "angle": 0, "type": "RoundRectBlock"}]}}