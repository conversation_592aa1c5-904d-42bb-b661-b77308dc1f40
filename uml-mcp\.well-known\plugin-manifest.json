{"schema_version": "v1", "name_for_human": "UML Diagram Generator", "name_for_model": "uml_diagram_generator", "description_for_human": "Generate UML and other types of diagrams directly from text descriptions.", "description_for_model": "This plugin helps you generate various types of diagrams from textual descriptions. It supports UML diagrams (Class, Sequence, Activity, etc.), Mermaid, D2, and more. You can generate diagrams by providing the diagram language and the code/description.", "auth": {"type": "none"}, "api": {"type": "openapi", "url": "https://uml-mcp.vercel.app/openapi.json"}, "logo_url": "https://uml-mcp.vercel.app/logo.png", "contact_email": "<EMAIL>", "legal_info_url": "https://example.com/legal"}