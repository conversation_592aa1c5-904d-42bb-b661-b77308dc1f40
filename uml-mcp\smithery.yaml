# Smithery.ai configuration
name: uml-mcp
version: "1.2.0"
description: "UML diagram generation server with MCP interface supporting PlantUML, Mermaid, Kroki, and D2"

startCommand:
  type: stdio
  configSchema:
    type: "object"
    properties:
      debug:
        type: "boolean"
        description: "Enable debug mode for verbose logging"
        default: false
      output_dir:
        type: "string"
        description: "Directory to store generated diagram files"
        default: "/app/output"
      transport:
        type: "string" 
        description: "Transport protocol to use (stdio or http)"
        enum: ["stdio", "http"]
        default: "stdio"
      host:
        type: "string"
        description: "Host to bind to when using HTTP transport"
        default: "127.0.0.1"
      port:
        type: "integer"
        description: "Port to bind to when using HTTP transport"
        default: 8000
        minimum: 1024
        maximum: 65535
      log_level:
        type: "string"
        description: "Logging level"
        enum: ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        default: "INFO"
      kroki_server:
        type: "string"
        description: "Kroki server URL for diagram rendering"
        default: "https://kroki.io"
      plantuml_server:
        type: "string"
        description: "PlantUML server URL for diagram rendering (leave empty to use Kroki)"
        default: ""
      enabled_formats:
        type: "array"
        description: "Enabled diagram formats (leave empty for all formats)"
        items:
          type: "string"
          enum: ["plantuml", "mermaid", "d2", "graphviz", "erd"]
        default: []
    additionalProperties: false
    description: "Configuration for UML diagram generation server"
  commandFunction: |-
    (config) => {
      // Build command arguments
      const args = ["mcp_server.py"];
      
      // Add transport option
      if (config.transport) {
        args.push("--transport", config.transport);
      }
      
      // Add debug flag if enabled
      if (config.debug) {
        args.push("--debug");
      }
      
      // Add host and port if using http transport
      if (config.transport === "http") {
        if (config.host) {
          args.push("--host", config.host);
        }
        if (config.port) {
          args.push("--port", config.port.toString());
        }
      }
      
      // Build environment variables
      const env = {
        "PYTHONUNBUFFERED": "1"  // Ensure unbuffered Python output
      };
      
      // Add output directory
      if (config.output_dir) {
        env.UML_MCP_OUTPUT_DIR = config.output_dir;
      }
      
      // Add log level
      if (config.log_level) {
        env.LOG_LEVEL = config.log_level;
      }
      
      // Add Kroki server
      if (config.kroki_server) {
        env.KROKI_SERVER = config.kroki_server;
      }
      
      // Add PlantUML server
      if (config.plantuml_server) {
        env.PLANTUML_SERVER = config.plantuml_server;
      }
      
      // Add enabled formats
      if (config.enabled_formats && config.enabled_formats.length > 0) {
        env.ENABLED_FORMATS = config.enabled_formats.join(",");
      }
      
      // Return command configuration
      return {
        command: "python",
        args: args,
        env: env
      };
    }
  exampleConfig: {
    "debug": false,
    "output_dir": "/app/output",
    "transport": "stdio",
    "log_level": "INFO"
  }
  healthCheck:
    interval: "30s"
    timeout: "5s"
    retries: 3
    startPeriod: "10s"

# Build configuration
build:
  dockerBuildPath: "../../"
  dockerfile: "./Dockerfile"

# Server capabilities based on actual console output
capabilities:
  # Server information
  serverName: "UML Diagram Generator"
  
  # Tools from console output
  tools:
    - name: "generate_uml"
      description: "Generate any UML diagram based on diagram type"
      parameters:
        - name: "diagram_type"
          type: "string"
          description: "Type of diagram (class, sequence, activity, etc.)"
          required: true
        - name: "code"
          type: "string"
          description: "The diagram code/description"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_class_diagram"
      description: "Generate UML class diagram from PlantUML code"
      parameters:
        - name: "code"
          type: "string"
          description: "The PlantUML diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_sequence_diagram"
      description: "Generate UML sequence diagram from PlantUML code"
      parameters:
        - name: "code"
          type: "string"
          description: "The PlantUML diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_activity_diagram"
      description: "Generate UML activity diagram from PlantUML code"
      parameters:
        - name: "code"
          type: "string"
          description: "The PlantUML diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_usecase_diagram"
      description: "Generate UML use case diagram from PlantUML code"
      parameters:
        - name: "code"
          type: "string"
          description: "The PlantUML diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_state_diagram"
      description: "Generate UML state diagram from PlantUML code"
      parameters:
        - name: "code"
          type: "string"
          description: "The PlantUML diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_component_diagram"
      description: "Generate UML component diagram from PlantUML code"
      parameters:
        - name: "code"
          type: "string"
          description: "The PlantUML diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_deployment_diagram"
      description: "Generate UML deployment diagram from PlantUML code"
      parameters:
        - name: "code"
          type: "string"
          description: "The PlantUML diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_object_diagram"
      description: "Generate UML object diagram from PlantUML code"
      parameters:
        - name: "code"
          type: "string"
          description: "The PlantUML diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_mermaid_diagram"
      description: "Generate diagrams using Mermaid syntax"
      parameters:
        - name: "code"
          type: "string"
          description: "The Mermaid diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_d2_diagram"
      description: "Generate diagrams using D2 syntax"
      parameters:
        - name: "code"
          type: "string"
          description: "The D2 diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_graphviz_diagram"
      description: "Generate diagrams using Graphviz DOT syntax"
      parameters:
        - name: "code"
          type: "string"
          description: "The Graphviz DOT code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
    
    - name: "generate_erd_diagram"
      description: "Generate Entity-Relationship diagrams"
      parameters:
        - name: "code"
          type: "string"
          description: "The ERD diagram code"
          required: true
        - name: "output_dir"
          type: "string"
          description: "Directory where to save the generated image"
          required: true
  
  # Prompts from console output
  prompts:
    - name: "class_diagram"
      description: "Create a UML class diagram showing classes, attributes, methods, and relationships"
    
    - name: "sequence_diagram"
      description: "Create a UML sequence diagram showing interactions between objects over time"
    
    - name: "activity_diagram"
      description: "Create a UML activity diagram showing workflows and business processes"
  
  # Resources from console output
  resources:
    - name: "uml://types"
      description: "List of available UML diagram types"
    
    - name: "uml://templates"
      description: "Templates for creating UML diagrams"
    
    - name: "uml://examples"
      description: "Example UML diagrams for reference"
    
    - name: "uml://formats"
      description: "Supported output formats for diagrams"
    
    - name: "uml://server-info"
      description: "Information about the UML-MCP server"