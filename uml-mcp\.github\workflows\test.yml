name: Run Tests

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.2
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Configure Poetry
        run: |
          poetry config virtualenvs.in-project true

      - name: Install dependencies
        run: |
          poetry install --with dev

      - name: Run tests
        run: |
          poetry run pytest --cov=mcp --cov-report=term --cov-report=html

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: htmlcov/
          retention-days: 14