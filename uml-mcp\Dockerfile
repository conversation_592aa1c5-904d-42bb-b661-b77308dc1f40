# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Install system dependencies for diagram tools
RUN apt-get update && apt-get install -y \
    graphviz \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file first for better caching
COPY requirements.txt ./

# Copy source code
COPY . .

# Create output directory
RUN mkdir -p /app/output

# Expose port for API
EXPOSE 8000

# Set entrypoint for stdio mode
ENTRYPOINT ["python", "mcp_server.py", "--transport", "stdio"]