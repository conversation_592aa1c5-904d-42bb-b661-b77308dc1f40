{"version": 2, "buildCommand": "pip install -r requirements.txt -r requirements-dev.txt", "installCommand": "pip install --upgrade pip", "framework": null, "outputDirectory": ".vercel/output/static", "builds": [{"src": "app.py", "use": "@vercel/python", "config": {"maxLambdaSize": "50mb", "runtime": "python3.10"}}, {"src": "favicon.ico", "use": "@vercel/static"}, {"src": ".well-known/**", "use": "@vercel/static"}], "routes": [{"src": "/.well-known/(.*)", "dest": "/.well-known/$1"}, {"src": "/logo.png", "dest": "/favicon.ico"}, {"src": "/(.*)", "dest": "/app.py"}], "env": {"KROKI_SERVER": "https://kroki.io", "VERCEL_OUTPUT_DIR": "/tmp/diagrams"}, "functions": {"app.py": {"includeFiles": "mcp/**,kroki/**,mermaid/**,plantuml/**,D2/**,*.py,*.json,*.ico,.well-known/**"}}}