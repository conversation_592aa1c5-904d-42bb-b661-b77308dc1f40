[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "uml-mcp"
version = "1.2.0"
description = "UML diagram generation server with MCP interface"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
license = "MIT"
repository = "https://github.com/yourusername/uml-mcp"
packages = [
    { include = "mcp" },
    { include = "kroki" },
    { include = "mermaid" },
    { include = "plantuml" },
    { include = "D2" },
    { include = "ai_uml" }
]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.103.1"
uvicorn = "^0.23.2"
pydantic = "^2.3.0"
rich = "^13.5.2"
httpx = "^0.25.0"
typer = "^0.9.0"
python-multipart = "^0.0.9"
python-dotenv = "^1.0.0"
starlette = "^0.27.0"
jinja2 = "^3.1.2"
aiofiles = "^23.2.1"
requests = "^2.28.0"
python-dateutil = "^2.8.2"
# Add diagram-related dependencies
graphviz = "^0.20.1"
pillow = "^10.1.0"
# If fastmcp is not your own module but a dependency
fastmcp = "^0.4.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.2"
pytest-cov = "^4.1.0"
pre-commit = "^3.4.0"
black = "^23.9.1"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.5.1"
pytest-asyncio = "^0.21.1"

[tool.poetry.scripts]
mcp-server = "mcp.core.server:main"
uml-mcp = "mcp.cli:app"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
python_classes = "Test*"
addopts = "--cov=mcp --cov-report=term --cov-report=html"

[tool.black]
line-length = 88
target-version = ["py39"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false

[tool.coverage.run]
source = ["mcp"]
omit = ["tests/*", "*/__init__.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
]