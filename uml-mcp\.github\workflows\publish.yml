name: UML-MCP CI/CD Pipeline

on:
  push:
    branches: [main]
    paths:
      - "mcp/**"
      - "kroki/**"
      - "mermaid/**"
      - "plantuml/**"
      - "D2/**"
      - "ai_uml/**"
      - "mcp_server.py"
      - "smithery.yaml"
      - "Dockerfile"
      - "pyproject.toml"
      - "requirements.txt"
      - ".github/workflows/**"
  pull_request:
    branches: [main]
  release:
    types: [created]
  workflow_dispatch:
    inputs:
      version_increment:
        description: 'Version increment type (patch, minor, major)'
        required: true
        default: 'patch'
      deploy_to_smithery:
        description: 'Deploy to Smithery after build'
        type: boolean
        required: false
        default: false
      publish_to_pypi:
        description: 'Publish to PyPI after build'
        type: boolean
        required: false
        default: false

jobs:
  test-and-build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11']
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.2
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Configure Poetry
        run: |
          poetry config virtualenvs.in-project true

      - name: Validate Python package structure
        run: |
          # Ensure every Python package has an __init__.py file
          for dir in mcp kroki mermaid plantuml D2 ai_uml; do
            if [ -d "$dir" ] && [ ! -f "$dir/__init__.py" ]; then
              echo "Creating missing __init__.py in $dir"
              touch "$dir/__init__.py"
            fi
          done

      - name: Install dependencies
        run: |
          poetry install --no-interaction --with dev
          # Ensure httpx is installed to avoid dependency issues
          poetry run pip install httpx>=0.25.0

      - name: List installed packages
        run: |
          poetry run pip list
          
      - name: Lint with flake8
        run: |
          poetry run flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          poetry run flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

      - name: Check typing with mypy
        run: |
          poetry run mypy mcp/

      - name: Run tests
        run: |
          poetry run pytest

      - name: Build package
        run: |
          poetry build
          echo "Built package for Python ${{ matrix.python-version }}"
          ls -la dist/

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: uml-mcp-dist-${{ matrix.python-version }}
          path: dist/
          retention-days: 5

  bump-version:
    needs: test-and-build
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.publish_to_pypi == 'true'
    runs-on: ubuntu-latest
    outputs:
      new_version: ${{ env.NEW_VERSION }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.2
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Bump version
        run: |
          # Get current version before bumping
          CURRENT_VERSION=$(poetry version -s)
          echo "Current version: $CURRENT_VERSION"
          
          # Bump version using Poetry
          poetry version ${{ github.event.inputs.version_increment }}
          NEW_VERSION=$(poetry version -s)
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_ENV
          echo "Version bumped to $NEW_VERSION"
          
          # Update version in config file if it exists
          VERSION_FILE="mcp/core/config.py"
          if [ -f "$VERSION_FILE" ]; then
            if grep -q "version = " "$VERSION_FILE"; then
              sed -i "s/version = \".*\"/version = \"$NEW_VERSION\"/" $VERSION_FILE
              echo "Updated version in $VERSION_FILE"
            else
              echo "Warning: Could not find version string in $VERSION_FILE"
            fi
          else
            echo "Warning: Config file $VERSION_FILE not found"
          fi
          
          # Commit and push changes
          git config --global user.name "GitHub Actions Bot"
          git config --global user.email "<EMAIL>"
          git add pyproject.toml
          [ -f "$VERSION_FILE" ] && git add $VERSION_FILE
          git commit -m "Bump version to $NEW_VERSION [skip ci]"
          git tag -a "v$NEW_VERSION" -m "Version $NEW_VERSION"
          git push origin main
          git push origin "v$NEW_VERSION"

  publish-to-pypi:
    needs: [test-and-build, bump-version]
    if: |
      (github.event_name == 'workflow_dispatch' && github.event.inputs.publish_to_pypi == 'true') ||
      github.event_name == 'release'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ github.event_name == 'workflow_dispatch' && 'main' || github.ref }}

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.2
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Configure Poetry
        run: |
          poetry config repositories.pypi https://upload.pypi.org/legacy/
          poetry config pypi-token.pypi ${{ secrets.PYPI_API_TOKEN }}

      - name: Get version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "NEW_VERSION=${{ needs.bump-version.outputs.new_version }}" >> $GITHUB_ENV
          else
            VERSION=$(poetry version -s)
            echo "NEW_VERSION=$VERSION" >> $GITHUB_ENV
          fi
          echo "Using version ${{ env.NEW_VERSION }}"

      - name: Build package
        run: |
          poetry build
          echo "Built package for version ${{ env.NEW_VERSION }}"
          ls -la dist/

      - name: Publish package to PyPI
        run: |
          poetry publish --repository pypi
          echo "Published version ${{ env.NEW_VERSION }} to PyPI"

      - name: Create release artifact
        uses: actions/upload-artifact@v4
        with:
          name: uml-mcp-${{ env.NEW_VERSION }}
          path: dist/
          retention-days: 30

  build-and-deploy-docker:
    needs: test-and-build
    if: |
      (github.event_name == 'push' && github.ref == 'refs/heads/main') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy_to_smithery == 'true')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          # Install only testing dependencies for validation
          grep -v "^mcp>=1.2.0" requirements.txt > requirements-filtered.txt
          grep -v "^mermaid-cli>=0.0.1" requirements-filtered.txt > requirements-clean.txt
          pip install -r requirements-clean.txt
          # Install httpx explicitly to avoid missing dependency error
          pip install httpx>=0.25.0

      - name: Verify setup
        run: |
          python -c "import fastapi, httpx, rich, pydantic, mcp_server"
          echo "Basic imports successful"

      - name: Install Smithery CLI
        run: |
          pip install smithery-cli
          smithery --version

      - name: Build Docker image
        run: |
          docker build -t uml-mcp:latest .
          echo "Docker build successful"
          
      - name: Test Docker image
        run: |
          docker run --rm -i uml-mcp:latest < /dev/null &
          sleep 5
          echo "Docker image test completed"
          
      - name: Log in to Smithery Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.SMITHERY_REGISTRY }}
          username: ${{ secrets.SMITHERY_USERNAME }}
          password: ${{ secrets.SMITHERY_TOKEN }}
          
      - name: Tag and push Docker image
        run: |
          # Tag with both latest and a version derived from the commit SHA
          SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-7)
          docker tag uml-mcp:latest ${{ secrets.SMITHERY_REGISTRY }}/uml-mcp:latest
          docker tag uml-mcp:latest ${{ secrets.SMITHERY_REGISTRY }}/uml-mcp:$SHORT_SHA
          
          # Push both tags
          docker push ${{ secrets.SMITHERY_REGISTRY }}/uml-mcp:latest
          docker push ${{ secrets.SMITHERY_REGISTRY }}/uml-mcp:$SHORT_SHA
          
      - name: Deploy to Smithery
        run: |
          # Deploy using smithery CLI
          smithery deploy --file smithery.yaml --environment production --token ${{ secrets.SMITHERY_TOKEN }}
          
      - name: Verify deployment
        run: |
          # Wait for deployment to complete and verify it's running
          smithery status --service uml-mcp --environment production --token ${{ secrets.SMITHERY_TOKEN }}