{"$schema": "http://json-schema.org/draft-07/schema#", "title": "DiagramConfig", "type": "object", "properties": {"name": {"type": "string"}, "version": {"type": "string"}, "nodes": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string"}, "x": {"type": "number"}, "y": {"type": "number"}, "width": {"type": "number"}, "height": {"type": "number"}, "angle": {"type": "number"}, "type": {"type": "string"}, "indent": {"type": "number"}, "textOrientation": {"type": "string", "enum": ["horizontal", "vertical_up", "vertical_down"]}}, "required": ["label"], "additionalProperties": true}}, "connections": {"type": "array", "items": {"type": "object", "properties": {"from": {"type": "string"}, "to": {"type": "string"}}, "required": ["from", "to"], "additionalProperties": false}}}, "required": ["name", "version", "nodes", "connections"], "additionalProperties": false}