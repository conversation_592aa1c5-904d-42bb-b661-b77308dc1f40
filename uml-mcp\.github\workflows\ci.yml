name: UML-MCP CI Pipeline

on:
  push:
    branches: [main]
    paths:
      - "mcp/**"
      - "kroki/**"
      - "mermaid/**"
      - "plantuml/**"
      - "D2/**"
      - "ai_uml/**"
      - "mcp_server.py"
      - "smithery.yaml"
      - "Dockerfile"
      - "pyproject.toml"
      - "requirements.txt"
      - ".github/workflows/**"
  pull_request:
    branches: [main]

jobs:
  test:
    uses: ./.github/workflows/test.yml

  build:
    needs: test
    uses: ./.github/workflows/build.yml